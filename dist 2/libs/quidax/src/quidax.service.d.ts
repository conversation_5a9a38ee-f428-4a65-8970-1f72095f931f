import { HttpService } from '@nestjs/axios';
import { Address, CreateOrder, CreateSubAccountDTO, CreateWithdrawalDTO, CryptoMarket, MarketData, MarketSummaryResponse, MarketTickerResponse, RecentMarketTradeResponse, TempSwapQuotationBody, SubAccount, UpdateSubAccount, Wallet, InstantSwapBody, SwapQuotationResponse, SwapTransactionResponse } from './quidax.interface';
export declare class QuidaxService {
    private readonly httpService;
    constructor(httpService: HttpService);
    createSubAccount(body: CreateSubAccountDTO): Promise<SubAccount>;
    getSubAccountById(user_id: string): Promise<SubAccount>;
    getSubaccounttByEmail(email: string): Promise<SubAccount>;
    getSubAccounts(): Promise<SubAccount[]>;
    getParentAccount(): Promise<SubAccount>;
    updateSubAccount(user_id: string, body: UpdateSubAccount): Promise<SubAccount>;
    fetchUserWallets(user_id: string): Promise<Wallet[]>;
    fetchWallet(user_id: string, currency: string): Promise<Wallet>;
    fetchWalletPaymentAddress(user_id: string, currency: string): Promise<Address>;
    fetchWalletPaymentAddresses(user_id: string, currency: string): Promise<Address[]>;
    fetchWalletAddressById(address_id: string, user_id: string, currency: string): Promise<Address>;
    validateAddress(currency: string, address: string): Promise<any>;
    createPaymentAddrressForACryptoCurrency(user_id: string, currency: string, network?: string): Promise<Address>;
    getCryptoMarkets(): Promise<CryptoMarket[]>;
    fetchMarketTickers(): Promise<MarketTickerResponse>;
    fetchAMarketTicker(cryptoCurrency: string): Promise<MarketData>;
    getMarketSummary(): Promise<MarketSummaryResponse>;
    transferCrypto(user_id: string, body: CreateWithdrawalDTO): Promise<any>;
    fetchWithdrawalsByReference(userId: string, reference: string): Promise<any>;
    createOrder(userId: string, body: CreateOrder): Promise<any>;
    fetchOrderDetails(userId: string, orderId: string): Promise<any>;
    cancelOrder(userId: string, orderId: string): Promise<any>;
    fetchRecentTradesForAMarket(marketPair: string): Promise<RecentMarketTradeResponse>;
    temporarySwapQuotation(user_id: string, tempSwaps: TempSwapQuotationBody): Promise<any>;
    initiateInstantSwap(user_id: string, body: InstantSwapBody): Promise<SwapQuotationResponse>;
    confirmInstantSwap(user_id: string, quotation_id: string): Promise<SwapTransactionResponse>;
    refreshSwapQuotation(user_id: string, quotationId: string, body: InstantSwapBody): Promise<SwapQuotationResponse>;
    fetchSwapTransaction(user_id: string, transaction_id: string): Promise<SwapTransactionResponse>;
}
