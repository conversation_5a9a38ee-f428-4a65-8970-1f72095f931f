{"version": 3, "file": "quidax.module.js", "sourceRoot": "", "sources": ["../../../../libs/quidax/src/quidax.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,yCAA2C;AAC3C,gDAAgC;AAChC,qDAAiD;AAkB1C,IAAM,YAAY,GAAlB,MAAM,YAAY;CAAG,CAAA;AAAf,oCAAY;uBAAZ,YAAY;IAhBxB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,kBAAU,CAAC,QAAQ,CAAC;gBAClB,OAAO,EAAE,MAAM;gBACf,YAAY,EAAE,CAAC;gBACf,OAAO,EAAE,gBAAM,CAAC,MAAM,CAAC,OAAO;gBAC9B,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,gBAAM,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC/C,cAAc,EAAE,kBAAkB;oBAClC,MAAM,EAAE,kBAAkB;iBAC3B;aACF,CAAC;SACH;QACD,SAAS,EAAE,CAAC,8BAAa,CAAC;QAC1B,OAAO,EAAE,CAAC,8BAAa,CAAC;KACzB,CAAC;GACW,YAAY,CAAG"}