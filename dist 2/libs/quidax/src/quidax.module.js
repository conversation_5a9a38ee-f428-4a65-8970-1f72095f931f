"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuidaxModule = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("../../../src/config");
const quidax_service_1 = require("./quidax.service");
let QuidaxModule = class QuidaxModule {
};
exports.QuidaxModule = QuidaxModule;
exports.QuidaxModule = QuidaxModule = __decorate([
    (0, common_1.Module)({
        imports: [
            axios_1.HttpModule.register({
                timeout: 100000,
                maxRedirects: 5,
                baseURL: config_1.default.quidax.baseUrl,
                headers: {
                    Authorization: `Bearer ${config_1.default.quidax.apiKey}`,
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
            }),
        ],
        providers: [quidax_service_1.QuidaxService],
        exports: [quidax_service_1.QuidaxService],
    })
], QuidaxModule);
//# sourceMappingURL=quidax.module.js.map