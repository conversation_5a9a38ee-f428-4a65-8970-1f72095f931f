export type PhoneNumber = `+234${number}`;
export interface CreateSubAccountDTO {
    email: string;
    first_name: string;
    last_name: string;
}
export interface SubAccount {
    id: string;
    sn: string;
    email: string;
    reference: string;
    first_name: string;
    last_name: string;
    display_name: string;
    created_at: string;
    updated_at: string;
}
export interface CreateSubAccountErrorResponse {
    code: string;
    message: string;
}
export interface SubAccountResponse {
    status: boolean;
    message: string;
    data: SubAccount;
}
export interface SubAccountMultipleResponse {
    status: boolean;
    message: string;
    data: SubAccount[];
}
export interface UpdateSubAccount {
    firstName: string;
    lastName: string;
    phone_number: PhoneNumber;
}
export interface Wallet {
    id: string;
    name: string;
    currency: string;
    balance: string;
    locked: string;
    staked: string;
    user: SubAccount;
    converted_balance: string;
    reference_currency: string;
    is_crypto: boolean;
    created_at: string;
    updated_at: string;
    blockchain_enabled: boolean;
    default_network: string;
    networks: Network[];
    deposit_address: string;
    destination_tag: string | null;
}
export interface Network {
    id: string;
    name: string;
    deposits_enabled: boolean;
    withdraws_enabled: boolean;
}
export interface Address {
    id: string;
    reference: string | null;
    currency: string;
    address: string;
    destination_tag: string | null;
    total_payments: string;
    created_at: string;
    updated_at: string;
    user: SubAccount;
    network: string;
}
export interface Ticker {
    buy: string;
    sell: string;
    low: string;
    high: string;
    open: string;
    last: string;
    vol: string;
}
export interface MarketData {
    at: number;
    ticker: Ticker;
}
export interface MarketTickerResponse {
    status: string;
    message: string;
    data: Record<string, MarketData>;
}
export interface MarketSummaryResponse {
    status: boolean;
    message: string;
    data: Record<string, MarketStats>;
}
export interface MarketStats {
    last_price: string;
    lowest_ask: string;
    highest_bid: string;
    base_volume: string;
    quote_volume: string;
    price_change_percent_24h: string;
    highest_price_24h: string;
    lowest_price_24h: string;
}
export interface TradingRules {
    base_precision: number;
    quote_precision: number;
    price_precision: number;
    minimum_order_size: number;
}
export interface CryptoMarket {
    id: string;
    name: string;
    base_unit: string;
    quote_unit: string;
    filters: Record<string, number>;
    trading_rules: TradingRules;
}
export interface CreateWithdrawalDTO {
    amount: string;
    currency: string;
    network?: string;
    fund_uid: string;
    fund_uid2?: string;
    transaction_note: string;
    reference: string;
    narration: string;
}
export interface Recipient {
    type: string;
    details: RecipientDetails;
}
export interface RecipientDetails {
    address: string;
    destination_tag: string | null;
    name: string | null;
}
export interface TransactionData {
    id: string;
    reference: string | null;
    type: string;
    currency: string;
    amount: string;
    fee: string;
    total: string;
    txid: string | null;
    transaction_note: string;
    narration: string;
    status: string;
    reason: string | null;
    created_at: string;
    done_at: string | null;
    recipient: Recipient;
    wallet: Wallet;
    user: SubAccount;
}
interface Market {
    id: string;
    base_unit: string;
    quote_unit: string;
}
interface PriceVolume {
    unit: string;
    amount: string;
}
interface Trade {
    id: string;
    market: Market;
    price: PriceVolume;
    volume: PriceVolume;
    total: PriceVolume;
    created_at: string;
    updated_at: string;
}
export interface Order {
    id: string;
    reference: string | null;
    market: Market;
    side: string;
    order_type: string;
    price: PriceVolume;
    avg_price: PriceVolume;
    volume: PriceVolume;
    origin_volume: PriceVolume;
    executed_volume: PriceVolume;
    status: string;
    trades_count: number;
    created_at: string;
    updated_at: string;
    done_at: string | null;
    user: SubAccount;
    trades: Trade[];
}
export interface CreateOrder {
    market: string;
    side: 'buy' | 'sell';
    ord_type: 'limit' | 'market';
    price?: string;
    volume: string;
    reference: string;
}
export interface RecentMarketTrade {
    type: 'buy' | 'sell';
    price: string;
    base_volume: string;
    quote_volume: string;
    trade_id: number;
    timestamp: number;
}
export interface RecentMarketTradeResponse {
    status: boolean;
    message: string;
    data: RecentMarketTrade[];
}
export interface TempSwapQuotationBody {
    from_currency: string;
    to_currency: string;
    from_amount: string;
}
export interface InstantSwapBody {
    from_currency: string;
    to_currency: string;
    from_amount: string;
}
export interface SwapQuotation {
    id: string;
    from_currency: string;
    to_currency: string;
    quoted_price: string;
    quoted_currency: string;
    from_amount: string;
    to_amount: string;
    confirmed: boolean;
    expires_at: string;
    created_at: string;
    updated_at: string;
    user: SubAccount;
}
export interface SwapQuotationResponse {
    status: string;
    message: string;
    data: SwapQuotation;
}
export interface SwapTransaction {
    id: string;
    from_currency: string;
    to_currency: string;
    from_amount: string;
    received_amount: string;
    execution_price: string;
    status: string;
    created_at: string;
    updated_at: string;
    swap_quotation: SwapQuotation;
    user: SubAccount;
}
export interface SwapTransactionResponse {
    status: string;
    message: string;
    data: SwapTransaction;
}
export {};
