import { RedisService } from '@crednet/utils';
export declare class InternalCacheService {
    private readonly redisService;
    private _tags;
    constructor(redisService: RedisService);
    tags(...tags: string[]): this;
    get<T>(key: string): Promise<T>;
    set<T>(key: string, value: T, ttl?: number): Promise<void>;
    delete(key: string): Promise<void>;
    flush(): Promise<void>;
    remember<T>(key: string, value: T | (() => Promise<T>), ttl?: number): Promise<T>;
    addToCollection(collection: string, key: string): Promise<void>;
    getAllFromCollection<T>(collection: string): Promise<T[]>;
    removeFromCollection(collection: string, key: string): Promise<void>;
    keys(pattern?: string): Promise<string[]>;
    hgetall(key: string): Promise<Record<string, string>>;
}
