"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InternalCacheService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("@crednet/utils");
let InternalCacheService = class InternalCacheService {
    constructor(redisService) {
        this.redisService = redisService;
        this._tags = [];
    }
    tags(...tags) {
        this._tags = [...this._tags, ...tags.flat()];
        return this;
    }
    async get(key) {
        const data = await this.redisService.get(key);
        let decoded;
        try {
            decoded = JSON.parse(data);
        }
        catch {
        }
        return decoded || data;
    }
    async set(key, value, ttl) {
        const stringValue = JSON.stringify(value);
        for (const tag of this._tags) {
            await this.redisService.getClient().sAdd(`tag:${tag}`, key);
        }
        const opts = ttl ? { EX: ttl } : {};
        await this.redisService.set(key, stringValue, opts);
    }
    async delete(key) {
        for (const tag of this._tags) {
            await this.redisService.getClient().sRem(`tag:${tag}`, key);
        }
        await this.redisService.del(key);
    }
    async flush() {
        if (this._tags.length < 1) {
            throw new Error('Flushing database not allowed.');
        }
        const tags = this._tags.map((tag) => `tag:${tag}`);
        const keys = await this.redisService.getClient().sUnion(tags);
        await this.redisService.getClient().del([...tags, ...keys]);
    }
    async remember(key, value, ttl) {
        const data = await this.get(key);
        if (data) {
            return data;
        }
        const newData = typeof value === 'function' ? await value() : value;
        await this.set(key, newData, ttl);
        return newData;
    }
    async addToCollection(collection, key) {
        await this.redisService.getClient().sAdd(collection, key);
    }
    async getAllFromCollection(collection) {
        const keys = await this.redisService.getClient().sMembers(collection);
        if (!keys.length)
            return [];
        const values = await Promise.all(keys.map(async (key) => {
            const data = await this.get(key);
            return data;
        }));
        return values.filter(Boolean);
    }
    async removeFromCollection(collection, key) {
        await this.redisService.getClient().sRem(collection, key);
    }
    async keys(pattern) {
        return this.redisService.getClient().keys(pattern || '*');
    }
    async hgetall(key) {
        return this.redisService.findOne('', key);
    }
};
exports.InternalCacheService = InternalCacheService;
exports.InternalCacheService = InternalCacheService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [utils_1.RedisService])
], InternalCacheService);
//# sourceMappingURL=internal-cache.service.js.map