import { Injectable } from '@nestjs/common';
import { SeederService } from './seeder.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class SeedersCron {
  constructor(private readonly seedersService: SeederService) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async createCurrencySeed() {
    console.log('Seeding data...');
    await this.seedersService.seed();
    console.log('Data seeded successfully.');
  }
}
