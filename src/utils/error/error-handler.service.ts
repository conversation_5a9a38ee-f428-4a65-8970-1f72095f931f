import { Injectable, Logger } from '@nestjs/common';
import { EventService } from '../events/event.service';

@Injectable()
export class ErrorHandlerService {
  private readonly logger = new Logger(ErrorHandlerService.name);

  constructor(private readonly eventService: EventService) {}

  async handleError(error: any, context: string, metadata: any = {}) {
    // Log the error
    this.logger.error(
      `Error in ${context}: ${error.message}`,
      error.stack,
    );

    // Add additional metadata
    const errorData = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date(),
      metadata,
    };

    // Emit error event for monitoring
    await this.eventService.emitEvent('system.error', errorData);

    // For critical errors, trigger alerts
    if (this.isCriticalError(error, context)) {
      await this.triggerCriticalErrorAlert(errorData);
    }

    return errorData;
  }

  async handleTransactionError(transactionId: string, error: any, context: string) {
    // Log transaction-specific error
    this.logger.error(
      `Transaction error for ${transactionId} in ${context}: ${error.message}`,
      error.stack,
    );

    // Emit transaction error event
    await this.eventService.emitEvent('transaction.error', {
      transactionId,
      error: error.message,
      context,
      timestamp: new Date(),
    });

    // Attempt recovery if possible
    if (this.canRecoverTransaction(error)) {
      await this.initiateTransactionRecovery(transactionId);
    }
  }

  private isCriticalError(error: any, context: string): boolean {
    // Logic to determine if an error is critical
    // For example, database connection failures, payment processing errors
    return (
      error.name === 'ConnectionError' ||
      context.includes('payment') ||
      context.includes('withdrawal')
    );
  }

  private async triggerCriticalErrorAlert(errorData: any) {
    // Send alert to monitoring system
    await this.eventService.emitEvent('system.critical_error', errorData);
    
    // Could also integrate with external alerting systems
    // e.g., PagerDuty, Slack, etc.
  }

  private canRecoverTransaction(error: any): boolean {
    // Logic to determine if a transaction can be recovered
    return !error.name.includes('Permanent') && 
           !error.message.includes('Invalid signature');
  }

  private async initiateTransactionRecovery(transactionId: string) {
    // Logic to initiate transaction recovery
    await this.eventService.emitEvent('transaction.recovery', {
      transactionId,
      timestamp: new Date(),
    });
  }
}