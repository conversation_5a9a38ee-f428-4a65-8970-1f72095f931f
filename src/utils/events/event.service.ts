import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Logger } from '@nestjs/common';

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(private readonly eventEmitter: EventEmitter2) {}

  async emitEvent(event: string, payload: any): Promise<void> {
    this.logger.debug(`Emitting event: ${event}`);
    this.eventEmitter.emit(event, payload);
  }

  async emitAsyncEvent(event: string, payload: any): Promise<void> {
    this.logger.debug(`Emitting async event: ${event}`);
    await this.eventEmitter.emitAsync(event, payload);
  }
}