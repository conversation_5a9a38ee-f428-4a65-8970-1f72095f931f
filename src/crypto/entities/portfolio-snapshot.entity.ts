import { Entity, Column, ManyToOne, Index } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export interface AssetSnapshot {
  currency: string;
  balance: string;
  value: string;
  percentage: number;
  priceChange24h: number;
}

@Entity('portfolio_snapshots')
@Index(['user', 'snapshotDate'])
export class PortfolioSnapshot extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column({ type: 'timestamp' })
  snapshotDate: Date;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  totalValue: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  totalValueUsd: string;

  @Column({ type: 'json' })
  assets: AssetSnapshot[];

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  dayChange: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  dayChangePercent: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  weekChange: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  weekChangePercent: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  monthChange: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  monthChangePercent: string;

  @Column({ type: 'int' })
  assetCount: number;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  profitLoss: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  profitLossPercent: string;
}
