import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { StakingRepository } from '../repositories/staking.repository';
import { UserRepository } from '../repositories/users.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';
import { StakingPosition, StakingType, StakingStatus } from '../entities/staking-position.entity';

export interface CreateStakingDto {
  currency: string;
  amount: string;
  stakingType: StakingType;
  lockPeriodDays?: number;
  stakingPoolId?: string;
  protocol?: string;
}

export interface StakingPoolInfo {
  id: string;
  name: string;
  currency: string;
  apy: string;
  stakingType: StakingType;
  lockPeriodDays?: number;
  minStakeAmount: string;
  maxStakeAmount?: string;
  protocol: string;
  isActive: boolean;
  totalStaked: string;
  description: string;
}

@Injectable()
export class StakingService {
  private readonly logger = new Logger(StakingService.name);

  constructor(
    private readonly stakingRepository: StakingRepository,
    private readonly userRepository: UserRepository,
    private readonly walletRepository: WalletRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async getAvailableStakingPools(): Promise<StakingPoolInfo[]> {
    // In a real implementation, this would fetch from various DeFi protocols
    // For now, return mock data
    return [
      {
        id: 'btc-flexible',
        name: 'Bitcoin Flexible Staking',
        currency: 'BTC',
        apy: '4.5',
        stakingType: StakingType.FLEXIBLE,
        minStakeAmount: '0.001',
        protocol: 'Quidax',
        isActive: true,
        totalStaked: '1250.5',
        description: 'Flexible Bitcoin staking with daily rewards',
      },
      {
        id: 'eth-locked-30',
        name: 'Ethereum 30-Day Lock',
        currency: 'ETH',
        apy: '6.2',
        stakingType: StakingType.LOCKED,
        lockPeriodDays: 30,
        minStakeAmount: '0.1',
        protocol: 'Quidax',
        isActive: true,
        totalStaked: '8750.25',
        description: '30-day locked Ethereum staking with higher rewards',
      },
      {
        id: 'usdt-defi',
        name: 'USDT DeFi Yield',
        currency: 'USDT',
        apy: '8.5',
        stakingType: StakingType.DEFI,
        minStakeAmount: '100',
        protocol: 'Compound',
        isActive: true,
        totalStaked: '2500000',
        description: 'DeFi yield farming with USDT',
      },
    ];
  }

  async createStakingPosition(auth: AuthData, dto: CreateStakingDto): Promise<StakingPosition> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Check wallet balance
    const wallet = await this.walletRepository.findOne({
      where: {
        user: { userId: auth.id.toString() },
        currency: { currencyCode: dto.currency },
      },
    });

    if (!wallet || parseFloat(wallet.balance) < parseFloat(dto.amount)) {
      throw new BadRequestException('Insufficient balance for staking');
    }

    // Get staking pool info
    const pools = await this.getAvailableStakingPools();
    const pool = pools.find(p => p.id === dto.stakingPoolId);
    
    if (!pool) {
      throw new BadRequestException('Invalid staking pool');
    }

    if (parseFloat(dto.amount) < parseFloat(pool.minStakeAmount)) {
      throw new BadRequestException(`Minimum stake amount is ${pool.minStakeAmount} ${dto.currency}`);
    }

    // Create staking position
    const stakingPosition = await this.stakingRepository.save({
      user,
      currency: dto.currency,
      amount: dto.amount,
      apy: pool.apy,
      stakingType: dto.stakingType,
      status: StakingStatus.ACTIVE,
      startDate: new Date(),
      lockPeriodDays: dto.lockPeriodDays,
      stakingPoolId: dto.stakingPoolId,
      stakingPoolName: pool.name,
      protocol: dto.protocol || pool.protocol,
      rewards: '0',
      totalRewardsEarned: '0',
      isActive: true,
    });

    // Deduct from wallet (in real implementation, this would be handled by the protocol)
    const newBalance = (parseFloat(wallet.balance) - parseFloat(dto.amount)).toString();
    await this.walletRepository.update(wallet.id, { balance: newBalance });

    this.logger.log(`Staking position created for user ${user.id}: ${dto.amount} ${dto.currency}`);

    return stakingPosition;
  }

  async getUserStakingPositions(auth: AuthData): Promise<StakingPosition[]> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return this.stakingRepository.findByUser(user.userId);
  }

  async getStakingStats(auth: AuthData): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const stats = await this.stakingRepository.getStakingStats(user.userId);
    const protocolBreakdown = await this.stakingRepository.getStakingByProtocol(user.userId);

    return {
      totalPositions: parseInt(stats.totalPositions || '0'),
      totalStaked: stats.totalStaked || '0',
      totalRewards: stats.totalRewards || '0',
      avgApy: parseFloat(stats.avgApy || '0').toFixed(2),
      protocolBreakdown,
    };
  }

  async requestUnstake(auth: AuthData, positionId: string): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const position = await this.stakingRepository.findOne({
      where: {
        id: positionId,
        user: { userId: user.userId },
        status: StakingStatus.ACTIVE,
      },
    });

    if (!position) {
      throw new BadRequestException('Staking position not found or not active');
    }

    // Check if lock period has expired for locked staking
    if (position.stakingType === StakingType.LOCKED && position.lockPeriodDays) {
      const lockEndDate = new Date(position.startDate);
      lockEndDate.setDate(lockEndDate.getDate() + position.lockPeriodDays);
      
      if (new Date() < lockEndDate) {
        throw new BadRequestException('Lock period has not expired yet');
      }
    }

    await this.stakingRepository.requestUnstake(positionId);

    this.logger.log(`Unstake requested for position ${positionId} by user ${user.id}`);

    return {
      message: 'Unstake request submitted successfully',
      estimatedCompletionTime: position.stakingType === StakingType.FLEXIBLE ? 'Immediate' : '1-3 business days',
    };
  }

  async claimRewards(auth: AuthData, positionId: string): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const position = await this.stakingRepository.findOne({
      where: {
        id: positionId,
        user: { userId: user.userId },
        status: StakingStatus.ACTIVE,
      },
    });

    if (!position) {
      throw new BadRequestException('Staking position not found or not active');
    }

    if (parseFloat(position.rewards) <= 0) {
      throw new BadRequestException('No rewards available to claim');
    }

    // Add rewards to wallet
    const wallet = await this.walletRepository.findOne({
      where: {
        user: { userId: auth.id.toString() },
        currency: { currencyCode: position.currency },
      },
    });

    if (wallet) {
      const newBalance = (parseFloat(wallet.balance) + parseFloat(position.rewards)).toString();
      await this.walletRepository.update(wallet.id, { balance: newBalance });
    }

    // Reset current rewards
    await this.stakingRepository.update(positionId, { rewards: '0' });

    this.logger.log(`Rewards claimed for position ${positionId} by user ${user.id}: ${position.rewards} ${position.currency}`);

    return {
      message: 'Rewards claimed successfully',
      amount: position.rewards,
      currency: position.currency,
    };
  }

  async calculateAndDistributeRewards(): Promise<void> {
    this.logger.log('Starting reward calculation and distribution...');

    const activePositions = await this.stakingRepository.findPositionsDueForRewards();

    for (const position of activePositions) {
      // Calculate daily reward based on APY
      const dailyRate = parseFloat(position.apy) / 365 / 100;
      const dailyReward = parseFloat(position.amount) * dailyRate;

      await this.stakingRepository.updateRewards(position.id, dailyReward.toString());

      this.logger.log(`Reward distributed: ${dailyReward} ${position.currency} to position ${position.id}`);
    }

    this.logger.log('Reward distribution completed');
  }

  async processExpiredLockPositions(): Promise<void> {
    this.logger.log('Processing expired lock positions...');

    const expiredPositions = await this.stakingRepository.findExpiredLockPositions();

    for (const position of expiredPositions) {
      // Auto-unstake expired locked positions
      await this.stakingRepository.completeUnstake(position.id);

      // Return staked amount + rewards to wallet
      const totalAmount = parseFloat(position.amount) + parseFloat(position.rewards);
      
      const wallet = await this.walletRepository.findOne({
        where: {
          user: { userId: position.user.userId },
          currency: { currencyCode: position.currency },
        },
      });

      if (wallet) {
        const newBalance = (parseFloat(wallet.balance) + totalAmount).toString();
        await this.walletRepository.update(wallet.id, { balance: newBalance });
      }

      this.logger.log(`Expired position auto-unstaked: ${position.id}, returned ${totalAmount} ${position.currency}`);
    }

    this.logger.log('Expired lock positions processing completed');
  }
}
