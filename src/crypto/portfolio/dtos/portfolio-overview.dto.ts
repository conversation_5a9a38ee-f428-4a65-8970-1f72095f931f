import { ApiProperty } from '@nestjs/swagger';

export class PortfolioOverviewDto {
  @ApiProperty({ description: 'Total portfolio value in USD' })
  totalValue: string;

  @ApiProperty({ description: '24h change in value' })
  dayChange: string;

  @ApiProperty({ description: '24h change percentage' })
  dayChangePercent: string;

  @ApiProperty({ description: 'Asset allocation breakdown' })
  assetAllocation: Array<{
    currency: string;
    percentage: number;
    value: string;
  }>;

  @ApiProperty({ description: 'Top performing assets' })
  topPerformers: Array<{
    currency: string;
    change: string;
    changePercent: string;
  }>;

  @ApiProperty({ description: 'Recent transactions' })
  recentTransactions: Array<{
    id: string;
    type: string;
    currency: string;
    amount: string;
    timestamp: Date;
  }>;
}
