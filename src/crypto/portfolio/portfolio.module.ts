import { Module } from '@nestjs/common';
import { PortfolioService } from './portfolio.service';
import { PortfolioController } from './portfolio.controller';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxModule } from '@app/quidax';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { InternalCacheModule } from '@app/internal-cache';
import { ErrorHandlerService } from '../../utils/error/error-handler.service';
import { EventService } from '../../utils/events/event.service';
import { InternalCacheService } from '@app/internal-cache';

@Module({
  imports: [QuidaxModule, InternalCacheModule],
  controllers: [PortfolioController],
  providers: [
    PortfolioService,
    PortfolioRepository,
    WalletRepository,
    TransactionRepository,
    UserRepository,
    CurrencyRepository,
    ErrorHandlerService,
    InternalCacheService,
    EventService,
  ],
})
export class PortfolioModule {}
