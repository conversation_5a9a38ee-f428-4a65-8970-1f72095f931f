import { Test, TestingModule } from '@nestjs/testing';
import { PortfolioService } from '../portfolio.service';
import { WalletRepository } from '../../repositories/wallet.repository';
import { TransactionRepository } from '../../repositories/transaction.repository';
import { QuidaxService } from '@app/quidax';
import { UserRepository } from '../../repositories/users.repository';
import { PortfolioRepository } from '../../repositories/portfolio.repository';
import { InternalCacheService } from '@app/internal-cache';
import { ErrorHandlerService } from '../../../utils/error/error-handler.service';
import { BadRequestException } from '@nestjs/common';

describe('PortfolioService', () => {
  let service: PortfolioService;
  let walletRepository: WalletRepository;
  let userRepository: UserRepository;
  let transactionRepository: TransactionRepository;
  let cacheService: InternalCacheService;
  let errorHandler: ErrorHandlerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortfolioService,
        {
          provide: WalletRepository,
          useValue: {
            getUserWallets: jest.fn(),
          },
        },
        {
          provide: TransactionRepository,
          useValue: {
            getTransactionsByUser: jest.fn(),
          },
        },
        {
          provide: QuidaxService,
          useValue: {
            getMarketSummary: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            getUserByUserId: jest.fn(),
          },
        },
        {
          provide: PortfolioRepository,
          useValue: {
            getHistoricalData: jest.fn(),
            getPerformanceMetrics: jest.fn(),
            saveSnapshot: jest.fn(),
          },
        },
        {
          provide: InternalCacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            tags: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: ErrorHandlerService,
          useValue: {
            handleError: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PortfolioService>(PortfolioService);
    walletRepository = module.get<WalletRepository>(WalletRepository);
    userRepository = module.get<UserRepository>(UserRepository);
    transactionRepository = module.get<TransactionRepository>(
      TransactionRepository,
    );
    cacheService = module.get<InternalCacheService>(InternalCacheService);
    errorHandler = module.get<ErrorHandlerService>(ErrorHandlerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPortfolioOverview', () => {
    it('should return portfolio overview for a user', async () => {
      // Mock data
      const userId = '123';
      const user = {
        id: '456',
        userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      } as any;

      const wallets = [
        {
          currency: { currencyCode: 'btc' },
          balance: '0.5',
          convertedBalance: '10000',
          user,
          locked: '0',
          staked: '0',
        },
        {
          currency: { currencyCode: 'eth' },
          balance: '2',
          convertedBalance: '5000',
          user,
          locked: '0',
          staked: '0',
        },
      ] as any;

      const transactions = [
        {
          id: 'tx1',
          type: 'fund',
          currency: 'btc',
          amount: '0.1',
          createdAt: new Date(),
          wallet: {},
          user: {},
          reference: 'ref1',
          network: 'bitcoin',
        },
      ] as any;

      // Mock cache miss
      jest.spyOn(cacheService, 'get').mockResolvedValue(undefined);
      jest.spyOn(cacheService, 'set').mockResolvedValue(undefined);

      // Mock repository responses
      jest.spyOn(userRepository, 'getUserByUserId').mockResolvedValue(user);
      jest.spyOn(walletRepository, 'getUserWallets').mockResolvedValue(wallets);
      jest
        .spyOn(transactionRepository, 'getTransactionsByUser')
        .mockResolvedValue({
          transactions,
          total: 1,
        });

      // Execute
      const result = await service.getPortfolioOverview({ id: userId } as any);

      // Assert
      expect(userRepository.getUserByUserId).toHaveBeenCalledWith(userId);
      expect(walletRepository.getUserWallets).toHaveBeenCalledWith(user.id);
      expect(result.totalValue).toBe('15000');
      expect(result.assetCount).toBe(2);
      expect(result.wallets).toHaveLength(2);
      expect(result.wallets[0].currency).toBe('btc');
      expect(result.assetAllocation).toHaveLength(2);
      expect(result.recentTransactions).toHaveLength(1);
      expect(cacheService.set).toHaveBeenCalled();
    });

    it('should return cached data when available', async () => {
      const userId = '123';
      const cachedData = {
        totalValue: '15000',
        assetCount: 2,
        assetAllocation: [],
        wallets: [],
        recentTransactions: [],
      };

      jest.spyOn(cacheService, 'get').mockResolvedValue(cachedData);

      const result = await service.getPortfolioOverview({ id: userId } as any);

      expect(result).toEqual(cachedData);
      expect(userRepository.getUserByUserId).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if user not found', async () => {
      jest.spyOn(cacheService, 'get').mockResolvedValue(undefined);
      jest.spyOn(userRepository, 'getUserByUserId').mockResolvedValue(null);

      await expect(
        service.getPortfolioOverview({ id: '123' } as any),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getPortfolioPerformance', () => {
    it('should return portfolio performance for a user', async () => {
      const userId = '123';
      const user = {
        id: '456',
        userId,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      } as any;

      const wallets = [
        {
          currency: { currencyCode: 'btc' },
          balance: '0.5',
          convertedBalance: '10000',
          user,
          locked: '0',
          staked: '0',
        },
      ] as any;

      const performanceMetrics = {
        startValue: '9000',
        endValue: '10000',
        valueChange: '1000',
        percentChange: '11.11',
        period: 'day',
        snapshots: [],
      };

      const historicalData = [
        {
          snapshotDate: new Date(),
          totalValue: '10000',
          dayChange: '1000',
          dayChangePercent: '11.11',
        },
      ] as any;

      // Mock cache miss
      jest.spyOn(cacheService, 'get').mockResolvedValue(undefined);
      jest.spyOn(cacheService, 'set').mockResolvedValue(undefined);

      // Mock repository responses
      jest.spyOn(userRepository, 'getUserByUserId').mockResolvedValue(user);
      jest.spyOn(walletRepository, 'getUserWallets').mockResolvedValue(wallets);
      jest
        .spyOn(service['portfolioRepository'], 'getPerformanceMetrics')
        .mockResolvedValue(performanceMetrics);
      jest
        .spyOn(service['portfolioRepository'], 'getHistoricalData')
        .mockResolvedValue(historicalData);

      const result = await service.getPortfolioPerformance({
        id: userId,
      } as any);

      expect(result.currentValue).toBe('10000');
      expect(result.dayChange).toBe('1000');
      expect(result.dayChangePercent).toBe('11.11');
      expect(result.historicalData).toHaveLength(1);
      expect(result.performanceMetrics.day).toEqual(performanceMetrics);
      expect(cacheService.set).toHaveBeenCalled();
    });

    it('should return cached performance data when available', async () => {
      const userId = '123';
      const cachedData = {
        currentValue: '10000',
        dayChange: '1000',
        dayChangePercent: '11.11',
        weekChange: '0',
        weekChangePercent: '0',
        monthChange: '0',
        monthChangePercent: '0',
        totalProfitLoss: '0',
        totalProfitLossPercent: '0',
        bestPerformer: null,
        worstPerformer: null,
        historicalData: [],
        performanceMetrics: { day: null, week: null, month: null },
      };

      jest.spyOn(cacheService, 'get').mockResolvedValue(cachedData);

      const result = await service.getPortfolioPerformance({
        id: userId,
      } as any);

      expect(result).toEqual(cachedData);
      expect(userRepository.getUserByUserId).not.toHaveBeenCalled();
    });
  });
});
