import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';
import { Wallet } from '../entities/wallet.entity';
import { InternalCacheService } from '@app/internal-cache';
import { ErrorHandlerService } from '../../utils/error/error-handler.service';
import { PortfolioOverviewDto } from './dtos/portfolio-overview.dto';
import { PortfolioPerformanceDto } from './dtos/portfolio-performance.dto';

@Injectable()
export class PortfolioService {
  private readonly logger = new Logger(PortfolioService.name);

  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
    private readonly portfolioRepository: PortfolioRepository,
    private readonly cacheService: InternalCacheService,
    private readonly errorHandler: ErrorHandlerService,
  ) {}

  async getPortfolioOverview(auth: AuthData): Promise<PortfolioOverviewDto> {
    const cacheKey = `portfolio:overview:${auth.id.toString()}`;

    try {
      // Try to get from cache first
      const cachedData =
        await this.cacheService.get<PortfolioOverviewDto>(cacheKey);
      if (cachedData) {
        this.logger.log(`Portfolio overview cache hit for user ${auth.id}`);
        return cachedData;
      }

      const user = await this.userRepository.getUserByUserId(
        auth.id.toString(),
      );
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const wallets = await this.walletRepository.getUserWallets(user.id);
      const totalValue = await this.calculatePortfolioValue(wallets);

      // Get recent transactions for overview
      const recentTransactions = await this.getRecentTransactions(user.id, 5);

      const overview: PortfolioOverviewDto = {
        totalValue: totalValue.toString(),
        assetCount: wallets.length,
        assetAllocation: this.calculateAssetAllocation(wallets, totalValue),
        wallets: wallets.map((wallet) => ({
          currency: wallet.currency.currencyCode,
          balance: wallet.balance,
          convertedBalance: wallet.convertedBalance,
        })),
        recentTransactions,
      };

      // Cache for 5 minutes
      await this.cacheService
        .tags('portfolio', 'overview', `user:${auth.id}`)
        .set(cacheKey, overview, 300);

      this.logger.log(`Portfolio overview generated for user ${auth.id}`);
      return overview;
    } catch (error) {
      await this.errorHandler.handleError(
        error,
        'PortfolioService.getPortfolioOverview',
        {
          userId: auth.id,
        },
      );
      throw error;
    }
  }

  private calculateAssetAllocation(wallets: Wallet[], totalValue: number) {
    return wallets.map((wallet) => ({
      currency: wallet.currency.currencyCode,
      percentage: (parseFloat(wallet.convertedBalance) / totalValue) * 100,
      value: wallet.convertedBalance,
      balance: wallet.balance,
    }));
  }

  private async getRecentTransactions(userId: string, limit: number = 5) {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30); // Last 30 days

      const { transactions } =
        await this.transactionRepository.getTransactionsByUser(
          userId,
          startDate,
          endDate,
          1,
          limit,
        );

      return transactions.map((transaction) => ({
        id: transaction.id,
        type: transaction.type,
        currency: transaction.currency,
        amount: transaction.amount,
        timestamp: transaction.createdAt,
      }));
    } catch (error) {
      this.logger.warn(
        `Failed to fetch recent transactions for user ${userId}:`,
        error,
      );
      return []; // Return empty array if transactions can't be fetched
    }
  }

  async getPortfolioPerformance(
    auth: AuthData,
  ): Promise<PortfolioPerformanceDto> {
    const cacheKey = `portfolio:performance:${auth.id.toString()}`;

    try {
      // Try to get from cache first
      const cachedData =
        await this.cacheService.get<PortfolioPerformanceDto>(cacheKey);
      if (cachedData) {
        this.logger.log(`Portfolio performance cache hit for user ${auth.id}`);
        return cachedData;
      }

      const user = await this.userRepository.getUserByUserId(
        auth.id.toString(),
      );
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const wallets = await this.walletRepository.getUserWallets(user.id);
      const currentValue = await this.calculatePortfolioValue(wallets);

      // Get performance metrics for different periods
      const dayMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'day',
      );
      const weekMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'week',
      );
      const monthMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'month',
      );

      // Get historical data for performance chart
      const historicalData = await this.portfolioRepository.getHistoricalData(
        user.id,
        30,
      );

      // Calculate best and worst performers
      const { bestPerformer, worstPerformer } =
        await this.calculateAssetPerformance(wallets);

      const performance: PortfolioPerformanceDto = {
        currentValue: currentValue.toString(),
        dayChange: dayMetrics?.valueChange || '0',
        dayChangePercent: dayMetrics?.percentChange || '0',
        weekChange: weekMetrics?.valueChange || '0',
        weekChangePercent: weekMetrics?.percentChange || '0',
        monthChange: monthMetrics?.valueChange || '0',
        monthChangePercent: monthMetrics?.percentChange || '0',
        totalProfitLoss: '0', // TODO: Calculate from initial investment
        totalProfitLossPercent: '0', // TODO: Calculate from initial investment
        bestPerformer,
        worstPerformer,
        historicalData: historicalData.map((snapshot) => ({
          date: snapshot.snapshotDate,
          value: snapshot.totalValue,
          change: snapshot.dayChange || '0',
          changePercent: snapshot.dayChangePercent || '0',
        })),
        performanceMetrics: {
          day: dayMetrics,
          week: weekMetrics,
          month: monthMetrics,
        },
      };

      // Cache for 10 minutes
      await this.cacheService
        .tags('portfolio', 'performance', `user:${auth.id}`)
        .set(cacheKey, performance, 600);

      this.logger.log(`Portfolio performance generated for user ${auth.id}`);
      return performance;
    } catch (error) {
      await this.errorHandler.handleError(
        error,
        'PortfolioService.getPortfolioPerformance',
        {
          userId: auth.id,
        },
      );
      throw error;
    }
  }

  private async calculateAssetPerformance(wallets: Wallet[]) {
    // This is a simplified implementation
    // In a real scenario, you'd compare current prices with historical prices
    let bestPerformer = null;
    let worstPerformer = null;

    if (wallets.length > 0) {
      // For now, just return the largest and smallest holdings
      const sortedByValue = wallets
        .filter((wallet) => parseFloat(wallet.convertedBalance) > 0)
        .sort(
          (a, b) =>
            parseFloat(b.convertedBalance) - parseFloat(a.convertedBalance),
        );

      if (sortedByValue.length > 0) {
        bestPerformer = {
          currency: sortedByValue[0].currency.currencyCode,
          change: '0', // TODO: Calculate actual change
          changePercent: '0', // TODO: Calculate actual change percent
        };

        worstPerformer = {
          currency:
            sortedByValue[sortedByValue.length - 1].currency.currencyCode,
          change: '0', // TODO: Calculate actual change
          changePercent: '0', // TODO: Calculate actual change percent
        };
      }
    }

    return { bestPerformer, worstPerformer };
  }

  private async calculatePortfolioValue(wallets: Wallet[]): Promise<number> {
    return wallets.reduce(
      (total, wallet) => total + parseFloat(wallet.convertedBalance),
      0,
    );
  }
}
