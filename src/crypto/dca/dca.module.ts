import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DcaService } from './dca.service';
import { DcaStrategyRepository } from '../repositories/dca-strategy.repository';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';
import { DcaStrategy } from '../entities/dca-strategy.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([DcaStrategy, User]),
    QuidaxModule,
  ],
  providers: [
    DcaService,
    DcaStrategyRepository,
    UserRepository,
  ],
  exports: [DcaService],
})
export class DcaModule {}
