import { Injectable } from '@nestjs/common';
import { DataSource, Between } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { PortfolioSnapshot } from '../entities/portfolio-snapshot.entity';
import { UserRepository } from './users.repository';

@Injectable()
export class PortfolioRepository extends TypeOrmRepository<PortfolioSnapshot> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
  ) {
    super(PortfolioSnapshot, dataSource.createEntityManager());
  }

  async getLatestSnapshot(userId: string): Promise<PortfolioSnapshot | null> {
    return this.findOne({
      where: {
        user: { userId },
      },
      order: {
        snapshotDate: 'DESC',
      },
      relations: ['user'],
    });
  }

  async getHistoricalData(
    userId: string,
    days: number = 30,
  ): Promise<PortfolioSnapshot[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    return this.find({
      where: {
        user: { userId },
        snapshotDate: Between(startDate, endDate),
      },
      order: {
        snapshotDate: 'ASC',
      },
      relations: ['user'],
    });
  }

  async getSnapshotByDate(
    userId: string,
    date: Date,
  ): Promise<PortfolioSnapshot | null> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return this.findOne({
      where: {
        user: { userId },
        snapshotDate: Between(startOfDay, endOfDay),
      },
      order: {
        snapshotDate: 'DESC',
      },
      relations: ['user'],
    });
  }

  async createSnapshot(
    userId: string,
    totalValue: string,
    totalValueUsd: string,
    assets: any[],
    performanceData: any,
  ): Promise<PortfolioSnapshot> {
    // Get the user first to ensure proper relationship
    const user = await this.userRepository.getUserByUserId(userId);

    const snapshot = this.create({
      user,
      snapshotDate: new Date(),
      totalValue,
      totalValueUsd,
      assets,
      assetCount: assets.length,
      ...performanceData,
    } as Partial<PortfolioSnapshot>);

    return this.save(snapshot);
  }

  async getPerformanceMetrics(
    userId: string,
    period: 'day' | 'week' | 'month' | 'year',
  ): Promise<any> {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    const snapshots = await this.find({
      where: {
        user: { userId },
        snapshotDate: Between(startDate, endDate),
      },
      order: {
        snapshotDate: 'ASC',
      },
      relations: ['user'],
    });

    if (snapshots.length < 2) {
      return null;
    }

    const firstSnapshot = snapshots[0];
    const lastSnapshot = snapshots[snapshots.length - 1];

    const valueChange =
      parseFloat(lastSnapshot.totalValue) -
      parseFloat(firstSnapshot.totalValue);
    const percentChange =
      (valueChange / parseFloat(firstSnapshot.totalValue)) * 100;

    return {
      startValue: firstSnapshot.totalValue,
      endValue: lastSnapshot.totalValue,
      valueChange: valueChange.toString(),
      percentChange: percentChange.toFixed(2),
      period,
      snapshots,
    };
  }
}
