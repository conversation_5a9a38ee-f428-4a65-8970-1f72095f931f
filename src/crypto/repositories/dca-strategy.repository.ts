import { Injectable } from '@nestjs/common';
import { DataSource, LessThanOrEqual } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { DcaStrategy, DcaStatus } from '../entities/dca-strategy.entity';

@Injectable()
export class DcaStrategyRepository extends TypeOrmRepository<DcaStrategy> {
  constructor(private readonly dataSource: DataSource) {
    super(DcaStrategy, dataSource.createEntityManager());
  }

  async findActiveStrategies(): Promise<DcaStrategy[]> {
    return this.find({
      where: {
        status: DcaStatus.ACTIVE,
      },
      relations: ['user'],
    });
  }

  async findDueStrategies(): Promise<DcaStrategy[]> {
    const now = new Date();
    return this.find({
      where: {
        status: DcaStatus.ACTIVE,
        nextExecutionDate: LessThanOrEqual(now),
      },
      relations: ['user'],
    });
  }

  async findByUser(userId: string): Promise<DcaStrategy[]> {
    return this.find({
      where: {
        user: { userId },
      },
      relations: ['user'],
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async updateExecutionStats(
    id: string,
    amountInvested: string,
    tokensAcquired: string,
    nextExecutionDate: Date,
  ): Promise<void> {
    const strategy = await this.findOne({ where: { id } });
    if (strategy) {
      await this.update(id, {
        lastExecutionDate: new Date(),
        nextExecutionDate,
        totalInvested: (parseFloat(strategy.totalInvested) + parseFloat(amountInvested)).toString(),
        totalTokensAcquired: (parseFloat(strategy.totalTokensAcquired) + parseFloat(tokensAcquired)).toString(),
        executionCount: strategy.executionCount + 1,
      });
    }
  }
}
